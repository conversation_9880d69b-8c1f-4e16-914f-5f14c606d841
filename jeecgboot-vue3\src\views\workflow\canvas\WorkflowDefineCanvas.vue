<template>
  <div class="content-main">
    <div class="tool-container">
      <div @click="undo" class="command" title="后退">
        <!-- <Icon icon="ant-design:undo-outlined" /> -->
        撤销
      </div>
      <!-- <div @click="redo" class="command" title="前进">
        <Icon icon="ant-design:redo-outlined" />
        前进
      </div> -->
      <el-divider direction="vertical" />
      <div @click="del" class="command" title="删除">
        <!-- <Icon icon="ant-design:delete-filled" /> -->
        删除
      </div>
      <el-divider direction="vertical" />
      <div @click="save" class="command" title="保存">
        <!-- <Icon icon="ant-design:save-filled" /> -->
        保存
      </div>
      <div @click="addLane" class="command" title="添加泳道">
        <!-- <Icon icon="ant-design:save-filled" /> -->
        添加泳道
      </div>

      <div @click="generateJson" class="command" title="生成JSON">JSON</div>
      <el-divider direction="vertical" />
    </div>
    <div class="content-container" id="">
      <div class="content">
        <div class="stencil" ref="stencilRef"></div>
        <div class="graph-content" id="graphContainer" ref="graphContainerRef"> </div>

        <!-- 新增右侧侧边栏 -->
        <div class="editor-sidebar" ref="sidebarRef">
          <div class="sidebar-header">
            <h3>上面的内容</h3>
          </div>
          <div class="sidebar-content">
            <!-- 侧边栏内容可根据需求填充 -->
            <p>侧边栏的内容</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  // import { Graph, Path, Edge, StringExt, Node, Cell, Model, DataUri } from '@antv/x6'
  import { Graph, Node, Edge, Shape } from '@antv/x6';
  import { Transform } from '@antv/x6-plugin-transform';
  import { Selection } from '@antv/x6-plugin-selection';
  import { Snapline } from '@antv/x6-plugin-snapline';

  import { Stencil } from '@antv/x6-plugin-stencil';
  import { queryById } from './WorkflowDefine.api';
  import { ref, onMounted, reactive, toRefs, nextTick, onUnmounted } from 'vue';
  import { any } from 'vue-types';
  import { useRoute } from 'vue-router';
  import { computed } from 'vue';
  import { watch } from 'fs';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { registerAllComponent } from '/@/components/jeecg/JVxeTable/src/utils/registerUtils';
  const isOpen = ref(false);
  const modal = ref();
  const route = useRoute();
  const data = reactive({
    id: '',
    businessKey: '',
    name: '',
    definitionView: '',
    isEffective: '',
    delFlag: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    definition: '',
    nodes: '',
    startNodeId: '',
    endNodeId: '',
    edges: '',
    version: '',
  });

  // const { createConfirm } = useMessage();
  //传给子组件
  // const tableInfo = ref([null])
  // const willShowData = ref({})
  // const showModal = ref(true);
  // const savedData = ref(null);
  // const componentName = ref('');
  // const cellFlag = ref(0);
  // const currentNodeId = ref(null);
  // 图表实例
  let graph: Graph | null = null;
  let stencil: Stencil | null = null;
  let laneCounter = 2;
  // const stencilContainer = ref()
  const stencilRef = ref<HTMLElement | null>(null);
  const graphContainer = ref();
  // DOM元素引用
  const graphContainerRef = ref<HTMLElement | null>(null);
  // 新增侧边栏引用
  const sidebarRef = ref<HTMLElement | null>(null);
  // const miniMapContainer = ref()
  // let graph: any =  ref()
  //操作节点数据
  // const nodeDataInfo = ref({nodes:[],edges: []})

  const graphOnEvent = () => {
    graph.on('cell:click', ({ e, x, y, cell, view }) => {});
    graph.on('node:click', ({ e, x, y, node, view }) => {});
    graph.on('edge:click', ({ e, x, y, edge, view }) => {});
    graph.on('blank:click', ({ e, x, y }) => {});

    graph.on('cell:mouseenter', ({ e, cell, view }) => {});
    graph.on('node:mouseenter', ({ e, node, view }) => {});
    graph.on('edge:mouseenter', ({ e, edge, view }) => {});
    graph.on('graph:mouseenter', ({ e }) => {});
  };

  // 初始化组件面板（核心修复：修复拖拽源配置）
  const initStencil = () => {
    if (!stencilRef.value || !graph) return;

    // 确保组件面板尺寸正确
    stencilRef.value.style.width = '200px';
    stencilRef.value.style.height = '100%';

    stencil = new Stencil({
      title: '流程图',
      target: graph, // 关键：绑定到画布实例
      stencilGraphWidth: 180,
      stencilGraphHeight: 170,
      collapsable: true,
      groups: [
        { title: '基础流程图', name: 'group1' },
        { title: '系统设计图', name: 'group2', graphHeight: 250 },
        // { title: '泳道节点', name: 'group3' }
      ],
      layoutOptions: {
        columns: 2,
        columnWidth: 80,
        rowHeight: 55,
      },
    });

    // 添加到DOM并加载组件
    stencilRef.value.appendChild(stencil.container);
    initShapes();
  };
  // 初始化图形组件（修复拖拽组件定义）
  const initShapes = () => {
    if (!graph || !stencil) return;

    // 定义连接桩
    const ports = {
      groups: {
        top: { position: 'top', attrs: { circle: { r: 4, magnet: true, stroke: '#5F95FF', fill: '#fff', style: { visibility: 'hidden' } } } },
        right: { position: 'right', attrs: { circle: { r: 4, magnet: true, stroke: '#5F95FF', fill: '#fff', style: { visibility: 'hidden' } } } },
        bottom: { position: 'bottom', attrs: { circle: { r: 4, magnet: true, stroke: '#5F95FF', fill: '#fff', style: { visibility: 'hidden' } } } },
        left: { position: 'left', attrs: { circle: { r: 4, magnet: true, stroke: '#5F95FF', fill: '#fff', style: { visibility: 'hidden' } } } },
      },
      items: [{ group: 'top' }, { group: 'right' }, { group: 'bottom' }, { group: 'left' }],
    };

    // 注册基础节点
    Graph.registerNode(
      'custom-rect',
      {
        inherit: 'rect',
        width: 66,
        height: 36,
        attrs: {
          body: { strokeWidth: 1, stroke: '#5F95FF', fill: '#EFF4FF' },
          text: { fontSize: 12, fill: '#262626' },
        },
        ports: { ...ports },
      },
      true
    );

    // 注册泳道节点
    Graph.registerNode(
      'lane',
      {
        inherit: 'rect',
        markup: [
          { tagName: 'rect', selector: 'body' },
          { tagName: 'rect', selector: 'name-rect' },
          { tagName: 'text', selector: 'name-text' },
        ],
        attrs: {
          body: { fill: '#FFF', stroke: '#5F95FF', strokeWidth: 1 },
          'name-rect': { width: 200, height: 30, fill: '#5F95FF', stroke: '#fff' },
          'name-text': { ref: 'name-rect', refY: 0.5, refX: 0.5, textAnchor: 'middle', fill: '#fff', fontSize: 12 },
        },
      },
      true
    );

    // 注册泳道内节点
    Graph.registerNode(
      'lane-rect',
      {
        inherit: 'rect',
        width: 100,
        height: 60,
        attrs: {
          body: { strokeWidth: 1, stroke: '#5F95FF', fill: '#EFF4FF' },
          text: { fontSize: 12, fill: '#262626' },
        },
        ports: { ...ports },
      },
      true
    );

    // 加载组件到面板
    loadStencilShapes();
  };

  // 加载组件到面板
  const loadStencilShapes = () => {
    if (!graph || !stencil) return;

    // 基础流程图节点
    const baseNodes = [
      graph.createNode({ shape: 'custom-rect', label: '开始', attrs: { body: { rx: 20, ry: 26 } } }),
      graph.createNode({ shape: 'custom-rect', label: '过程' }),
      graph.createNode({ shape: 'custom-rect', label: '可选过程', attrs: { body: { rx: 6, ry: 6 } } }),
      graph.createNode({ shape: 'custom-rect', label: '决策', attrs: { body: { rx: 40, ry: 20 } } }),
      graph.createNode({ shape: 'custom-rect', label: '数据' }),
      graph.createNode({ shape: 'custom-rect', label: '链接' }),
    ];
    stencil.load(baseNodes, 'group1');

    const systemNodes = [
      graph.createNode({ shape: 'custom-rect', label: '判断', attrs: { body: { rx: 20, ry: 26 } } }),
      graph.createNode({ shape: 'custom-rect', label: '流程' }),
      graph.createNode({ shape: 'custom-rect', label: '链接节点', attrs: { body: { rx: 6, ry: 6 } } }),
    ];
    stencil.load(systemNodes, 'group2');

    // 泳道相关节点
    const laneNodes = [
      graph.createNode({ shape: 'lane', width: 180, height: 100, attrs: { 'name-text': { text: '泳道' } } }),
      graph.createNode({ shape: 'lane-rect', label: '泳道节点' }),
    ];
    stencil.load(laneNodes, 'group3');
  };

  // 控制连接桩显示/隐藏
  const initPortVisibility = () => {
    if (!graph || !graphContainerRef.value) return;

    const showPorts = (ports: NodeListOf<SVGElement>, show: boolean) => {
      ports.forEach((port) => (port.style.visibility = show ? 'visible' : 'hidden'));
    };

    graph.on('node:mouseenter', () => {
      const ports = graphContainerRef.value!.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>;
      showPorts(ports, true);
    });

    graph.on('node:mouseleave', () => {
      const ports = graphContainerRef.value!.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>;
      showPorts(ports, false);
    });
  };
  // 初始化图表（核心修复：确保容器尺寸正确）
  const initGraph = () => {
    if (!graphContainerRef.value) return;

    // 强制设置容器尺寸
    graphContainerRef.value.style.width = 'calc(100% - 200px)';
    graphContainerRef.value.style.height = '100%';

    // 创建图表实例
    graph = new Graph({
      container: graphContainerRef.value,
      width: graphContainerRef.value.clientWidth, // 关键：设置画布宽度
      height: graphContainerRef.value.clientHeight, // 关键：设置画布高度
      grid: {
        size: 10,
        visible: true,
        type: 'dot',
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3,
      },
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: { radius: 8 },
        },
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        snap: { radius: 20 },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: { name: 'block', width: 12, height: 8 },
              },
            },
            zIndex: 0,
          });
        },
        validateConnection({ targetMagnet }) {
          return !!targetMagnet;
        },
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: { attrs: { fill: '#5F95FF', stroke: '#5F95FF' } },
        },
      },
      translating: {
        restrict(cellView: any) {
          const cell = cellView.cell as Node;
          const parentId = cell.prop('parent');
          if (parentId) {
            const parentNode = graph?.getCellById(parentId) as Node;
            if (parentNode) {
              return parentNode.getBBox().moveAndExpand({
                x: 0,
                y: 30,
                width: 0,
                height: -30,
              });
            }
          }
          return cell.getBBox();
        },
      },
    });

    // 初始化插件
    if (graph) {
      graph
        .use(new Transform({ resizing: true, rotating: true }))
        .use(new Selection({ rubberband: true, showNodeSelectionBox: true }))
        .use(new Snapline());

      initPortVisibility();
    }
    loadData();
  };

  // 注册连线--左右
  // Graph.registerConnector(
  //   'curveConnectorLR',
  //   (sourcePoint, targetPoint) => {
  //     const hgap = Math.abs(targetPoint.x - sourcePoint.x)
  //     const path = new Path()
  //     path.appendSegment(Path.createSegment('M', sourcePoint.x - 4, sourcePoint.y))
  //     path.appendSegment(Path.createSegment('L', sourcePoint.x + 12, sourcePoint.y))
  //     // 水平三阶贝塞尔曲线
  //     path.appendSegment(
  //       Path.createSegment(
  //         'C',
  //         sourcePoint.x < targetPoint.x ? sourcePoint.x + hgap / 2 : sourcePoint.x - hgap / 2,
  //         sourcePoint.y,
  //         sourcePoint.x < targetPoint.x ? targetPoint.x - hgap / 2 : targetPoint.x + hgap / 2,
  //         targetPoint.y,
  //         targetPoint.x - 6,
  //         targetPoint.y
  //       )
  //     )
  //     path.appendSegment(Path.createSegment('L', targetPoint.x + 2, targetPoint.y))

  //     return path.serialize()
  //   },
  //   true
  // )

  // Graph.registerEdge(
  //   'processing-curve',
  //   {
  //     inherit: 'edge',
  //     markup: [
  //       {
  //         tagName: 'path',
  //         selector: 'wrap',
  //         attrs: {
  //           fill: 'none',
  //           cursor: 'pointer',
  //           stroke: 'transparent',
  //           strokeLinecap: 'round'
  //         }
  //       },
  //       {
  //         tagName: 'path',
  //         selector: 'line',
  //         attrs: {
  //           fill: 'none',
  //           pointerEvents: 'none'
  //         }
  //       }
  //     ],
  //     connector: { name: 'smooth' }, //curveConnectorTB
  //     attrs: {
  //       wrap: {
  //         connection: true,
  //         strokeWidth: 10,
  //         strokeLinejoin: 'round'
  //       },
  //       line: {
  //         connection: true,
  //         stroke: '#A2B1C3',
  //         strokeWidth: 1,
  //         targetMarker: {
  //           name: 'classic',
  //           size: 6
  //         }
  //       }
  //     }
  //   },
  //   true
  // )

  // Graph.registerEdge(
  //   'processing-curve-lr',
  //   {
  //   inherit: 'edge',
  //   markup: [
  //       {
  //         tagName: 'path',
  //         selector: 'wrap',
  //         attrs: {
  //           fill: 'none',
  //           cursor: 'pointer',
  //           stroke: 'transparent',
  //           strokeLinecap: 'round',
  //         },
  //       },
  //       {
  //         tagName: 'path',
  //         selector: 'line',
  //         attrs: {
  //           fill: 'none',
  //           pointerEvents: 'none',
  //         },
  //       },
  //     ],
  //     connector: { name: 'curveConnectorLR' },
  //     attrs: {
  //       wrap: {
  //         connection: true,
  //         strokeWidth: 10,
  //         strokeLinejoin: 'round',
  //       },
  //       line: {
  //         connection: true,
  //         stroke: '#A2B1C3',
  //         strokeWidth: 1,
  //         targetMarker: {
  //           name: 'classic',
  //           size: 6,
  //         },
  //       },
  //     },
  // },
  //   true,
  // )

  //保存
  // function save() {
  //   // console.log('save')
  //   const graphData = graph.toJSON()
  //   // console.log(graphData.cells)
  //   //保存节点所有信息（节点信息和表信息，连接线信息）
  //   //遍历所有节点
  //   let cells = graphData.cells
  //   let node_info = {nodes:[],edges:[]}
  //   let nodes = []
  //   let edges = []
  //   node_info.nodes = nodes
  //   node_info.edges= edges
  //   var edgeStatusList = []
  //   for(var i=0;i<cells.length;i++){
  //     //获取节点信息
  //     if(cells[i].shape === 'custom-node'){
  //       var node = {id: cells[i].id , position: cells[i].position,size: cells[i].size,data: cells[i].data,attrs:cells[i].attrs}
  //       nodes.push(node)
  //     }
  //     //获取连线信息
  //     if(cells[i].shape === 'processing-curve'){
  //       var edge = {id: cells[i].id , shape: cells[i].shape , source: cells[i].source , target: cells[i].target , data: cells[i].data }
  //       edges.push(edge)
  //       var edgeStatus = {id: cells[i].id , status: 'success'}
  //       edgeStatusList.push(edgeStatus)
  //     }
  //   }
  //   // console.log('node_info = '+ JSON.stringify(node_info))
  //   // console.log('edgeStatusList = '+ JSON.stringify(edgeStatusList))
  //   //保存节点结果信息（节点信息，根据该信息生成SQL）
  //   state.data = node_info
  //   state.edgeStatusList = edgeStatusList
  //   // console.log('sqlList = '+JSON.stringify(sqlList))
  //   showSql()
  //   // console.log('sqlList = '+JSON.stringify(sqlList))
  //   // 关联模型ID,保存到数据库
  //   var ModelSql = {'id':mode_id,modeNo:getModeNo(),modeNodeInfo:JSON.stringify(nodes),modeEdgeInfo:JSON.stringify(edges),modeSql:JSON.stringify(sqlList)};
  //   var isUpdate = false
  //   if(mode_id == ''){
  //     isUpdate = false
  //     // ModelSql = {modeNo:'RM_100001',modeNodeInfo:JSON.stringify(nodes),modeEdgeInfo:JSON.stringify(edges),modeSql:JSON.stringify(sqlList)};
  //   }else{
  //     isUpdate = true

  //   }

  //   saveOrUpdate(ModelSql,isUpdate).then((res) => {
  //       if (res.success) {
  //         alert('已保存！')
  //       } else {

  //       }
  //     });
  // }

  //撤销
  function undo() {
    if (graph.canUndo()) {
      graph.undo();
    }
    return false;
  }
  //取消撤销
  function redo() {
    if (graph.canRedo()) {
      graph.redo();
    }
    return false;
  }
  //复制
  function copy() {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.copy(cells);
    }
    return false;
  }
  //粘贴
  function paste() {
    if (!graph.isClipboardEmpty()) {
      const cells = graph.paste({ offset: 32 });
      graph.cleanSelection();
      graph.select(cells);
    }
    return false;
  }
  //删除
  function del() {
    // console.log('删除节点')
    const cells = graph.getSelectedCells();
    // console.log('选中节点个数：'+ cells.length)
    if (cells.length) {
      graph.removeCells(cells);
    }
  }

  //导出PNG
  function exportPng() {
    graph.toPNG(
      (dataUri: string) => {
        // 下载
        DataUri.downloadDataUri(dataUri, 'chart.png');
      },
      {
        padding: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 20,
        },
      }
    );
    //graph.exportPNG('a.png',{padding:'20px'});
  }

  // 添加泳道

  // function addLane() {
  // console.log('添加泳道')
  // }

  // 添加泳道
  const addLane = () => {
    if (!graph) return;

    // 获取现有泳道
    const lanes = graph.getCells().filter((cell) => cell.shape === 'lane');

    // 计算新泳道的位置
    let x = 10;
    if (lanes.length > 0) {
      // 找到最右边的泳道
      const rightmostLane = lanes.reduce((prev, current) => {
        return prev.getBBox().x + prev.getBBox().width > current.getBBox().x + current.getBBox().width ? prev : current;
      });
      x = rightmostLane.getBBox().x + rightmostLane.getBBox().width + 10;
    }

    // 创建新泳道
    laneCounter++;
    const newLane = graph.createNode({
      id: `lane${laneCounter}`,
      shape: 'lane',
      x,
      y: 10,
      width: 300,
      height: 500,
      attrs: {
        'name-text': {
          text: `部门${laneCounter}`,
        },
        body: {
          fill: '#FFF',
          stroke: '#5F95FF',
          strokeWidth: 1,
        },
        'name-rect': {
          width: 300,
          height: 30,
          fill: '#5F95FF',
          stroke: '#fff',
          strokeWidth: 1,
          x: -1,
        },
      },
    });

    // 添加新泳道到图表
    graph.addNode(newLane);

    // 调整视图以适应新泳道
    graph.zoomToFit({ padding: 10, maxScale: 1 });
  };

  // 注册节点和边
  const registerNodesAndEdges = () => {
    // 注册泳道节点
    Graph.registerNode(
      'lane',
      {
        inherit: 'rect',
        markup: [
          {
            tagName: 'rect',
            selector: 'body',
          },
          {
            tagName: 'rect',
            selector: 'name-rect',
          },
          {
            tagName: 'text',
            selector: 'name-text',
          },
        ],
        attrs: {
          body: {
            fill: '#FFF',
            stroke: '#5F95FF',
            strokeWidth: 1,
          },
          'name-rect': {
            width: 200,
            height: 30,
            fill: '#5F95FF',
            stroke: '#fff',
            strokeWidth: 1,
            x: -1,
          },
          'name-text': {
            ref: 'name-rect',
            refY: 0.5,
            refX: 0.5,
            textAnchor: 'middle',
            fontWeight: 'bold',
            fill: '#fff',
            fontSize: 12,
          },
        },
      },
      true
    );

    // 注册矩形节点
    Graph.registerNode(
      'lane-rect',
      {
        inherit: 'rect',
        width: 100,
        height: 60,
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#5F95FF',
            fill: '#EFF4FF',
          },
          text: {
            fontSize: 12,
            fill: '#262626',
          },
        },
      },
      true
    );

    // 注册多边形节点
    Graph.registerNode(
      'lane-polygon',
      {
        inherit: 'polygon',
        width: 80,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#5F95FF',
            fill: '#EFF4FF',
            refPoints: '0,10 10,0 20,10 10,20',
          },
          text: {
            fontSize: 12,
            fill: '#262626',
          },
        },
      },
      true
    );

    // 注册边
    Graph.registerEdge(
      'lane-edge',
      {
        inherit: 'edge',
        attrs: {
          line: {
            stroke: '#A2B1C3',
            strokeWidth: 2,
          },
        },
        label: {
          attrs: {
            label: {
              fill: '#A2B1C3',
              fontSize: 12,
            },
          },
        },
      },
      true
    );
  };

  // 加载数据
  const loadData = async () => {
    if (!graph) return;

    try {
      const cells: Cell[] = [];
      const res = await queryById({ id: route.params.id });
      Object.assign(data, res);
      const swimlaneData = JSON.parse(data.definitionView);
      const laneMap = new Map(); // 创建泳道ID映射表

      // 1. 先创建泳道节点
      swimlaneData.lanes.forEach((lane: any) => {
        const laneNode = graph!.createNode({
          id: lane.id,
          shape: 'lane',
          x: lane.x,
          y: lane.y,
          width: lane.width,
          height: lane.height,
          attrs: {
            'name-text': {
              text: lane.title,
            },
            ...lane.attrs,
          },
        });
        cells.push(laneNode);
        // 存储泳道位置信息用于坐标转换
        laneMap.set(lane.id, {
          x: lane.x,
          y: lane.y,
          width: lane.width,
          height: lane.height,
        });
      });

      // 2. 处理节点（关键修改：坐标转换）
      swimlaneData.nodes.forEach((node: any) => {
        // 获取所属泳道信息
        const parentLane = laneMap.get(node.parent);

        let relativeX = node.x;
        let relativeY = node.y;

        // 转换为相对坐标（考虑30px标题栏偏移）
        if (parentLane) {
          relativeX = parentLane.x;
          // relativeY = node.y - parentLane.y - 30; // 减去标题栏高度
        }

        const nodeItem = graph!.createNode({
          id: node.id,
          shape: 'lane-rect',
          x: relativeX, // 使用转换后的相对X坐标
          y: relativeY, // 使用转换后的相对Y坐标
          width: node.width,
          height: node.height,
          attrs: {
            text: {
              text: node.label,
            },
            ...node.attrs,
          },
          parent: node.parent, // 确保parent属性指向泳道ID
        });
        cells.push(nodeItem);
      });

      // 3. 处理边（保持不变）
      swimlaneData.edges.forEach((edge: any) => {
        const edgeItem = graph!.createEdge({
          id: edge.id,
          shape: 'lane-edge',
          source: { cell: edge.source },
          target: { cell: edge.target },
          labels: edge.labels,
          attrs: edge.attrs,
        });
        cells.push(edgeItem);
      });

      graph.resetCells(cells);
      graph.zoomToFit({ padding: 10, maxScale: 1 });
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  };

  const generateJson = () => {
    if (!graph) return;
    // 获取画布中所有节点和边的JSON数据
    const graphJson = graph.toJSON();
    // 处理JSON数据（仅保留关键信息）
    const exportData = {
      nodes: graphJson.cells.filter((cell: any) => cell.shape && cell.shape.includes('rect')), // 筛选节点
      edges: graphJson.cells.filter((cell: any) => cell.source && cell.target), // 筛选边（连接关系）
    };
    // 导出为JSON字符串并展示/下载
    const jsonString = JSON.stringify(exportData, null, 2);
    // 方式1：在控制台打印
    console.log('导出的JSON数据：', jsonString);
    // const blob = new Blob([jsonString], { type: 'application/json' });
    // const url = URL.createObjectURL(blob);
    // const a = document.createElement('a');
    // a.href = url;
    // a.download = 'flowchart.json'; // 下载文件名
    // a.click();
    // URL.revokeObjectURL(url);
  };

  onMounted(() => {
    setTimeout(() => {
      registerNodesAndEdges();
      initGraph();
      initStencil();
      loadData();
      graphOnEvent();
    }, 0);
    // init()
    // console.log("state.data.nodes = "+JSON.stringify(state.data.nodes))
    // graph.fromJSON(state.data);
    // getData()
    // showEdgeStatus()
    // const path = computed(() => route.params.modeNo)

    // initData(getModeNo())
  });

  onUnmounted(() => {
    graph.dispose();
  });
</script>

<style lang="less" scoped>
  .content-main {
    display: flex;
    width: 100%;
    flex-direction: column;
    height: calc(100vh - 85px - 40px);
    background-color: #ffffff;
    position: relative;

    .tool-container {
      padding: 8px;
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.45);

      .command {
        display: inline-block;
        width: 60px;
        height: 30px;
        margin: 0 6px;
        padding-top: 6px;
        text-align: center;
        cursor: pointer;
      }
    }
  }
  .content-container {
    position: relative;
    width: 100%;
    height: 100%;
    .content {
      width: 100%;
      height: 100%;
      position: relative;

      min-width: 400px;
      min-height: 600px;
      display: flex;
      border: 1px solid #dfe3e8;
      flex-direction: row;
      //   flex-wrap: wrap;
      flex: 1 1;

      .stencil {
        width: 400px;
        height: 100%;
        border-right: 1px solid #dfe3e8;
        position: relative;
        :deep(.x6-widget-stencil) {
          background-color: #fff;
        }
        :deep(.x6-widget-stencil-title) {
          background-color: #fff;
        }
        :deep(.x6-widget-stencil-group-title) {
          background-color: #fff !important;
        }
      }
      .graph-content {
        width: calc(100% - 180px);
        height: 100%;
      }
      // 右侧侧边栏样式
      .editor-sidebar {
        width: 200px;
        flex-shrink: 0; // 禁止收缩
        border-left: 1px solid #dfe3e8;
        background-color: #fff;
        display: flex;
        flex-direction: column;

        .sidebar-header {
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;
          h3 {
            margin: 0;
            font-size: 14px;
            color: #333;
          }
        }

        .sidebar-content {
          padding: 16px;
          flex: 1;
          overflow-y: auto;
          font-size: 12px;
          color: #666;
        }
      }

      // .editor-sidebar {
      //   display: flex;
      //   flex-direction: column;
      //   border-left: 1px solid #e6f7ff;
      //   background: #fafafa;
      //   z-index: 9;

      //   .el-card {
      //     border: none;
      //   }
      //   .edit-panel {
      //     flex: 1 1;
      //     background-color: #fff;
      //   }

      //   :deep(.x6-widget-minimap-viewport) {
      //     border: 1px solid #8f8f8f;
      //   }

      //   :deep(.x6-widget-minimap-viewport-zoom) {
      //     border: 1px solid #8f8f8f;
      //   }
      // }
    }
  }

  :deep(.x6-widget-transform) {
    margin: -1px 0 0 -1px;
    padding: 0px;
    border: 1px solid #239edd;
  }
  :deep(.x6-widget-transform > div) {
    border: 1px solid #239edd;
  }
  :deep(.x6-widget-transform > div:hover) {
    background-color: #3dafe4;
  }
  :deep(.x6-widget-transform-active-handle) {
    background-color: #3dafe4;
  }
  :deep(.x6-widget-transform-resize) {
    border-radius: 0;
  }
  :deep(.x6-widget-selection-inner) {
    border: 1px solid #239edd;
  }
  :deep(.x6-widget-selection-box) {
    opacity: 0;
  }
  :deep(.x6-widget-stencil-search-text) {
    z-index: 0;
  }
  .topic-image {
    visibility: hidden;
    cursor: pointer;
  }
  .x6-node:hover .topic-image {
    visibility: visible;
  }
  .x6-node-selected rect {
    stroke-width: 2px;
  }
</style>
